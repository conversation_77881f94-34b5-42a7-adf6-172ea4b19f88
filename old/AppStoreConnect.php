<?php

namespace App\Libs\Apple;

use App\Libs\GoogleCloud;
use App\Libs\Logging\LoggingMethods;
use App\Models\App;
use App\Models\AppleDeveloper;
use App\Models\CustomerReview;
use App\Services\RegionService;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Collection;
use <PERSON>cobucci\JWT\Configuration;
use <PERSON><PERSON>bu<PERSON>\JWT\Signer\Ecdsa\Sha256;
use <PERSON><PERSON><PERSON>cci\JWT\Signer\Key\InMemory;

class AppStoreConnect
{
    use LoggingMethods;

    private AppleDeveloper $developer;

    private $httpProxy;
    private $requestTimeout = 30;

    private RegionService $regionSvc;

    public function __construct(AppleDeveloper $developer)
    {
        $this->developer = $developer;
        if (empty($this->developer->ascInfo->api_issuer_id)) {
            throw new Exception("Invalid asc info for developer #{$this->developer->id}: missing `api_issuer_id`");
        }
        $this->httpProxy = $this->developer->getAppleRequestHttpProxy();
        if (empty($this->httpProxy)) {
            throw new Exception("Empty http proxy for apple developer #{$developer->id} {$developer->name}");
        }
        $this->regionSvc = new RegionService();
    }

    /**
     * @param $adamId
     * @param int $maxCount
     * @return Collection<CustomerReview>
     */
    public function syncCustomerReviews($adamId, $maxCount = 100)
    {
        $this->ascDebug("Begin for adamId: $adamId, maxCount: $maxCount");
        $app = App::of($adamId);
        if (empty($app)) {
            throw new Exception("App not found: $adamId");
        }
        if ($app->developer_id !== $this->developer->id) {
            throw new Exception("App and developer mismatch. App: #$adamId {$app->name}, developer: #{$this->developer->id} {$this->developer->name}");
        }
        $maxCount = min(2000, $maxCount);
        $accessToken = $this->reloadAccessToken();
        $httpClient = new Client([
            'proxy' => $this->httpProxy,
            'timeout' => $this->requestTimeout,
            'http_errors' => false,
        ]);
        $itemCount = 0;
        $result = collect([]);
        $pageSize = min(200, $maxCount);
        $nextPageUrl = "https://api.appstoreconnect.apple.com/v1/apps/$adamId/customerReviews?include=response&limit=$pageSize&sort=-createdDate";
        while ($itemCount < $maxCount) {
            $this->ascDebug("maxCount: $maxCount, pageSize: $pageSize, nextPageUrl: $nextPageUrl");
            $res = $httpClient->get($nextPageUrl, [
                'headers' => ['Authorization' => "Bearer $accessToken"]
            ]);
            $statusCode = $res->getStatusCode();
            if ($statusCode !== 200) {
                throw new Exception("Failed to get customer reviews. Status code: $statusCode");
            }
            $resBody = (string)$res->getBody();
            $resData = json_decode($resBody, true);
            $itemCount += count($resData['data']);
            $result = $result->merge($this->normalizeAndSyncCustomerReviews($adamId, $app->name, $resData['data'], $resData['included'] ?? null));
            $nextPageUrl = $resData['links']['next'] ?? null;
            if (empty($nextPageUrl)) {
                break;
            }
        }
        $this->ascDebug("End with result count: {$result->count()}");
        $app->update(['asc_review_sync_time' => now()]);
        return $result;
    }

    public function updateCustomerReviewResponseBody($adamId, $reviewId, $responseBody, $operatorName = null)
    {
        $this->ascDebug("Begin for adamId: $adamId, reviewId: $reviewId, responseBody: $responseBody");
        $app = App::of($adamId);
        if (empty($app)) {
            throw new Exception("App not found: $adamId");
        }
        if ($app->developer_id !== $this->developer->id) {
            throw new Exception("App and developer mismatch. App: #$adamId {$app->name}, developer: #{$this->developer->id} {$this->developer->name}");
        }
        $accessToken = $this->reloadAccessToken();
        $httpClient = new Client([
            'proxy' => $this->httpProxy,
            'timeout' => $this->requestTimeout,
            'http_errors' => false,
        ]);
        $res = $httpClient->post('https://api.appstoreconnect.apple.com/v1/customerReviewResponses', [
            'headers' => ['Authorization' => "Bearer $accessToken"],
            'json' => [
                'data' => [
                    'type' => 'customerReviewResponses',
                    'relationships' => ['review' => ['data' => ['id' => $reviewId, 'type' => 'customerReviews']]],
                    'attributes' => ['responseBody' => $responseBody]
                ],
            ],
        ]);
        $statusCode = $res->getStatusCode();
        $resBody = (string)$res->getBody();
        if ($statusCode !== 200 && $statusCode !== 201) {
            throw new Exception("Failed to update customer review response. Status code: $statusCode, body: $resBody");
        }
        $resData = json_decode($resBody, true);
        if (!empty($resData['data'] && $resData['data']['type'] === 'customerReviewResponses')) {
            $responseEntry = $resData['data'];
            $responseId = $responseEntry['id'];
            $responseState = $responseEntry['attributes']['state'];
            $responseBody = $responseEntry['attributes']['responseBody'];
            $googleCloud = new GoogleCloud();
            $translationResult = $googleCloud->translate($responseBody, 'zh');
            $responseBodyTrans = $translationResult['text'];
            try {
                $responseLastModifiedDate = new Carbon($responseEntry['attributes']['lastModifiedDate']);
                $responseLastModifiedDate->utc();
            } catch (Exception $e) {
                $responseLastModifiedDate = now()->utc();
            }
            $review = CustomerReview::where('review_id', $reviewId)->first();
            if ($review) {
                $review->update([
                    'response_id' => $responseId,
                    'response_state' => $responseState,
                    'response_body' => $responseBody,
                    'response_body_trans' => $responseBodyTrans,
                    'response_last_modified_date' => (string)$responseLastModifiedDate,
                    'response_operator_name' => $operatorName,
                ]);
            }
        }
        $this->ascDebug("End");
    }

    private function normalizeAndSyncCustomerReviews($adamId, $appName, $data, $included, $translate = true)
    {
        $records = [];
        $customerReviewResponsesIdMap = [];
        if (!empty($included)) {
            foreach ($included as $includedEntry) {
                if ($includedEntry['type'] === 'customerReviewResponses') {
                    $customerReviewResponsesIdMap[$includedEntry['id']] = $includedEntry;
                }
            }
        }
        $googleCloud = new GoogleCloud();
        foreach ($data as $reviewEntry) {
            $territory = $reviewEntry['attributes']['territory'];
            $regionId = $this->regionSvc->getRegionIdByCode3($territory) ?? 'us';
            $createdDate = new Carbon($reviewEntry['attributes']['createdDate']);
            $createdDate->utc();
            $title = $reviewEntry['attributes']['title'];
            $body = $reviewEntry['attributes']['body'];
            $reviewerNickname = $reviewEntry['attributes']['reviewerNickname'];
            $responseId = $reviewEntry['relationships']['response']['data']['id'] ?? null;
            if ($responseId && !empty($customerReviewResponsesIdMap[$responseId])) {
                $responseEntry = $customerReviewResponsesIdMap[$responseId];
                $responseState = $responseEntry['attributes']['state'];
                $responseBody = $responseEntry['attributes']['responseBody'];
                $responseLastModifiedDate = new Carbon($responseEntry['attributes']['lastModifiedDate']);
                $responseLastModifiedDate->utc();
            } else {
                $responseState = null;
                $responseBody = null;
                $responseLastModifiedDate = null;
            }
            $matchId = CustomerReview::getMatchId($adamId, $regionId, $reviewerNickname, $createdDate);
            $records[] = [
                'developer_id' => $this->developer->id,
                'adam_id' => $adamId,
                'app_name' => $appName,
                'review_id' => $reviewEntry['id'],
                'match_id' => $matchId,
                'region_id' => $regionId,
                'territory' => $territory,
                'rating' => $reviewEntry['attributes']['rating'],
                'title' => $title,
                'body' => $body,
                'title_trans' => null,
                'body_trans' => null,
                'reviewer_nickname' => $reviewerNickname,
                'created_date' => (string)$createdDate,
                'response_id' => $responseId,
                'response_state' => $responseState,
                'response_body' => $responseBody,
                'response_body_trans' => null,
                'response_last_modified_date' => $responseLastModifiedDate ? (string)$responseLastModifiedDate : null,
            ];
        }
        if ($translate) {
            $strings = [];
            foreach ($records as $record) {
                $strings[] = $record['title'] ?: '';
                $strings[] = $record['body'] ?: '';
                $strings[] = $record['response_body'] ?: '';
            }
            try {
                $translationResult = $googleCloud->batchTranslate($strings, 'zh');
            } catch (Exception $e) {
                $translationResult = [];
            }
            foreach ($records as $i => &$record) {
                $transText = $translationResult[$i * 3]['text'] ?? null;
                $record['title_trans'] = $transText ?: null;
                $transText = $translationResult[$i * 3 + 1]['text'] ?? null;
                $record['body_trans'] = $transText ?: null;
                $transText = $translationResult[$i * 3 + 2]['text'] ?? null;
                $record['response_body_trans'] = $transText ?: null;
            }
            unset($record);
        }
        $reviewIds = [];
        foreach ($records as $record) {
            $reviewIds[] = $record['review_id'];
            CustomerReview::upsert($record, ['review_id'], [
                'app_name', 'region_id', 'match_id', 'territory',
                'rating', 'title', 'body', 'title_trans', 'body_trans', 'reviewer_nickname',
                'response_id', 'response_state', 'response_body', 'response_body_trans', 'response_last_modified_date',
            ]);
        }
        $result = CustomerReview::whereIn('review_id', $reviewIds)
            ->orderBy('created_date', 'desc')
            ->get();
        return $result;
    }

    public function listApps()
    {
        $accessToken = $this->reloadAccessToken();
        $httpClient = new Client([
            'proxy' => $this->httpProxy,
            'timeout' => $this->requestTimeout,
            'http_errors' => false,
        ]);
        $res = $httpClient->get('https://api.appstoreconnect.apple.com/v1/apps', [
            'headers' => ['Authorization' => "Bearer $accessToken"],
            'query' => ['include' => 'appStoreVersions'],
        ]);
        $statusCode = $res->getStatusCode();
        $resBody = (string)$res->getBody();
        $resData = json_decode($resBody, true);
        return [$statusCode, $resData, $resBody];
    }

    public function sendConsumptionInformation($appBundleId, $transactionId, array $consumptionInfo)
    {
        $reqData = [
            'accountTenure' => $consumptionInfo['accountTenure'],
            'appAccountToken' => $consumptionInfo['appAccountToken'] ?: '',
            'customerConsented' => $consumptionInfo['customerConsented'],
            'consumptionStatus' => $consumptionInfo['consumptionStatus'],
            'deliveryStatus' => $consumptionInfo['deliveryStatus'],
            'lifetimeDollarsPurchased' => $consumptionInfo['lifetimeDollarsPurchased'],
            'lifetimeDollarsRefunded' => $consumptionInfo['lifetimeDollarsRefunded'],
            'platform' => $consumptionInfo['platform'],
            'playTime' => $consumptionInfo['playTime'],
            'refundPreference' => $consumptionInfo['refundPreference'],
            'sampleContentProvided' => $consumptionInfo['sampleContentProvided'],
            'userStatus' => $consumptionInfo['userStatus'],
        ];
        $accessToken = $this->createServerApiToken($appBundleId);
        $httpClient = new Client([
            'proxy' => $this->httpProxy,
            'timeout' => $this->requestTimeout,
            'http_errors' => false,
        ]);
        $url = "https://api.storekit.itunes.apple.com/inApps/v1/transactions/consumption/$transactionId";
        $res = $httpClient->put($url, [
            'headers' => ['Authorization' => "Bearer $accessToken"],
            'json' => $reqData,
        ]);
        $statusCode = $res->getStatusCode();
        $resBody = (string)$res->getBody();
        $resData = json_decode($resBody, true);
        return [$statusCode, $resData, $resBody];
    }

    public function listTerritories()
    {
        $accessToken = $this->reloadAccessToken();
        $httpClient = new Client([
            'proxy' => $this->httpProxy,
            'timeout' => $this->requestTimeout,
            'http_errors' => false,
        ]);
        $res = $httpClient->get('https://api.appstoreconnect.apple.com/v1/territories?limit=200', [
            'headers' => ['Authorization' => "Bearer $accessToken"],
        ]);
        $statusCode = $res->getStatusCode();
        $resBody = (string)$res->getBody();
        $resData = json_decode($resBody, true);
        return [$statusCode, $resData, $resBody];
    }

    public function getYearlyTotalProceeds($year)
    {
        if (empty($this->developer->payment_vendor_number)) {
            return 0;
        }
        $accessToken = $this->reloadAccessToken();
        $httpClient = new Client([
            'proxy' => $this->httpProxy,
            'timeout' => $this->requestTimeout,
            'http_errors' => false,
        ]);
        $res = $httpClient->get('https://api.appstoreconnect.apple.com/v1/salesReports', [
            'headers' => [
                'Authorization' => "Bearer $accessToken",
                'Accept' => 'application/a-gzip',
                'Accept-Encoding' => 'gzip',
            ],
            'query' => [
                'filter[frequency]' => 'YEARLY',
                'filter[reportDate]' => $year,
                'filter[reportType]' => 'SALES',
                'filter[reportSubType]' => 'SUMMARY',
                'filter[vendorNumber]' => $this->developer->payment_vendor_number,
            ],
            'decode_content' => false,
        ]);
        $statusCode = $res->getStatusCode();
        $resBody = gzdecode((string)$res->getBody());
        // XXX Not available
    }

    /**
     * 每次调用 App Store Connect API 之前重新读取最新的 access token
     */
    private function reloadAccessToken()
    {
        $this->developer->load('ascInfo');
        $ascInfo = $this->developer->ascInfo;
        return $ascInfo->api_access_token;
    }

    public function refreshAccessToken()
    {
        $ascInfo = $this->developer->ascInfo;
        if (empty($ascInfo->api_issuer_id)) {
            throw new Exception("Failed to refresh access token for developer #{$this->developer->id}: missing `api_issuer_id`");
        }

        $keyId = $ascInfo->api_key_id;
        $apiPrivateKey = $ascInfo->api_private_key;
        $issuerId = $ascInfo->api_issuer_id;
        $issuedAt = now();
        $expiresAt = $issuedAt->copy()->addMinutes(15);

        $jwtConfig = Configuration::forAsymmetricSigner(
            new Sha256(),
            InMemory::plainText($apiPrivateKey),
            InMemory::plainText('')
        );
        $jwtBuilder = $jwtConfig->builder();
        $jwtBuilder->withHeader('alg', 'ES256');
        $jwtBuilder->withHeader('kid', $keyId);
        $jwtBuilder->withHeader('typ', 'JWT');
        $jwtBuilder->issuedBy($issuerId);
        $jwtBuilder->issuedAt($issuedAt->toDateTimeImmutable());
        $jwtBuilder->expiresAt($expiresAt->toDateTimeImmutable());
        $jwtBuilder->permittedFor('appstoreconnect-v1');
        $token = $jwtBuilder->getToken($jwtConfig->signer(), $jwtConfig->signingKey());
        $signedToken = (string)$token;

        $ascInfo->update([
            'api_access_token' => $signedToken,
            'api_access_token_updated_at' => now(),
            'api_access_token_expires_at' => $expiresAt,
        ]);
    }

    public function createServerApiToken($appBundleId)
    {
        $ascInfo = $this->developer->ascInfo;
        if (empty($ascInfo->api_issuer_id)) {
            throw new Exception("Failed to refresh access token for developer #{$this->developer->id}: missing `api_issuer_id`");
        }
        $keyId = $ascInfo->api_key_id;
        $apiPrivateKey = $ascInfo->api_private_key;
        $issuerId = $ascInfo->api_issuer_id;
        $issuedAt = now();
        $expiresAt = $issuedAt->copy()->addMinutes(15);
        $jwtConfig = Configuration::forAsymmetricSigner(
            new Sha256(),
            InMemory::plainText($apiPrivateKey),
            InMemory::plainText('')
        );
        $jwtBuilder = $jwtConfig->builder();
        $jwtBuilder->withHeader('alg', 'ES256');
        $jwtBuilder->withHeader('kid', $keyId);
        $jwtBuilder->withHeader('typ', 'JWT');
        $jwtBuilder->issuedBy($issuerId);
        $jwtBuilder->issuedAt($issuedAt->toDateTimeImmutable());
        $jwtBuilder->expiresAt($expiresAt->toDateTimeImmutable());
        $jwtBuilder->permittedFor('appstoreconnect-v1');
        $jwtBuilder->withClaim('bid', $appBundleId);
        $token = $jwtBuilder->getToken($jwtConfig->signer(), $jwtConfig->signingKey());
        $signedToken = (string)$token;
        return $signedToken;
    }
}
