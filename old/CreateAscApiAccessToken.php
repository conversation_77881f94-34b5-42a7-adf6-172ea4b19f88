<?php

namespace App\Console\Commands\Asc;

use App\Console\Commands\LegacyBaseCommand;
use App\Libs\Apple\AppStoreConnect;
use App\Models\AppleDeveloper;

class CreateAscApiAccessToken extends LegacyBaseCommand
{
    protected $signature = 'create-asc-api-access-token {developer_id}';

    private $developerId;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->onCommandStart();

        $this->developerId = (int)$this->argument('developer_id');
        $this->logInfo("developerId: {$this->developerId}");

        $developer = AppleDeveloper::find($this->developerId);
        $this->logInfo("Developer: {$developer->id} {$developer->name}");
        $appStoreConnect = new AppStoreConnect($developer);
        $appStoreConnect->refreshAccessToken();

        $this->onCommandFinish();
    }

}
