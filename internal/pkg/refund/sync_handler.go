package refund

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"midas/internal/config"
	"midas/internal/models"
	"midas/internal/pkg/binlog"
	"midas/internal/pkg/logx"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

// SyncHandler 退款同步处理器
type SyncHandler struct {
	db     *gorm.DB
	logger logx.Logger
}

// NewSyncHandler 创建新的同步处理器
func NewSyncHandler(db *gorm.DB, logger logx.Logger) *SyncHandler {
	return &SyncHandler{
		db:     db,
		logger: logger,
	}
}

// HandleAppleServerNotification 处理apple_server_notifications表的变更
func (h *SyncHandler) HandleAppleServerNotification(ctx context.Context, event *binlog.ChangeEvent) error {
	// 只处理插入和更新操作
	if !event.IsInsertOrUpdate() {
		return nil
	}

	// 构建通知对象
	notification := h.buildNotificationFromEvent(event)
	if notification == nil {
		return nil
	}

	// 检查是否为退款相关通知
	if !notification.IsRefundRelated() {
		h.logger.WithFields(map[string]interface{}{
			"notification_id":   notification.ID,
			"notification_type": safeStr(notification.NotificationType),
			"environment":       safeStr(notification.Environment),
		}).Debug("skip non-refund related notification")
		return nil
	}

	h.logger.WithFields(map[string]interface{}{
		"notification_id":   notification.ID,
		"notification_type": safeStr(notification.NotificationType),
		"database":          event.Database,
	}).Info("processing refund notification")

	// 提取transaction_id
	transactionID := h.extractTransactionIDFromNotification(notification)
	if transactionID == "" {
		h.logger.Warn("failed to extract transaction_id from notification")
		return nil
	}

	// 查找对应的App配置
	appConfig, err := h.findAppConfigByDatabase(ctx, event.Database)
	if err != nil {
		h.logger.WithError(err).Warn("failed to find app config")
		return nil
	}

	if appConfig == nil || appConfig.AdamID == nil {
		h.logger.Warn("app config not found or adam_id is nil")
		return nil
	}

	// 根据通知类型分别处理
	if notification.IsConsumptionRequest() {
		// CONSUMPTION_REQUEST 通知：创建新的退款请求记录
		// 这相当于PHP中CohortSyncRefundRequests的逻辑，从通知中提取信息创建退款请求
		h.logger.WithFields(map[string]interface{}{
			"notification_id": notification.ID,
			"transaction_id":  transactionID,
		}).Info("processing CONSUMPTION_REQUEST notification to create refund request")
		return h.handleConsumptionRequestNotification(ctx, notification, appConfig)
	}

	// REFUND/REFUND_DECLINED 通知：查找现有退款请求并更新状态
	var refundRequest models.RefundRequest
	err = h.db.WithContext(ctx).
		Where("adam_id = ? AND transaction_id = ?", *appConfig.AdamID, transactionID).
		First(&refundRequest).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			h.logger.WithFields(map[string]interface{}{
				"adam_id":        *appConfig.AdamID,
				"transaction_id": transactionID,
			}).Info("refund request not found for notification")
			return nil
		}
		return fmt.Errorf("failed to query refund request: %w", err)
	}

	// 检查终态保护
	if h.isTerminalState(&refundRequest) {
		h.logger.WithFields(map[string]interface{}{
			"refund_request_id": refundRequest.ID,
			"refund_result":     safeStr(refundRequest.RefundResult),
		}).Info("refund request is in terminal state, skip")
		return nil
	}

	// 根据通知类型处理
	return h.processNotificationForRequest(ctx, notification, &refundRequest, appConfig)
}

// buildNotificationFromEvent 从事件构建通知对象
func (h *SyncHandler) buildNotificationFromEvent(event *binlog.ChangeEvent) *models.AppleServerNotification {
	// 明确优先使用 After 数据，其次回退到 Before（防止部分更新只带变更列导致缺失）
	if event == nil || (len(event.After) == 0 && len(event.Before) == 0) {
		return nil
	}

	// 局部读取辅助：优先 After，再回退 Before
	getRaw := func(field string) (interface{}, bool) {
		if event.After != nil {
			if v, ok := event.After[field]; ok && v != nil {
				return v, true
			}
		}
		if event.Before != nil {
			if v, ok := event.Before[field]; ok && v != nil {
				return v, true
			}
		}
		return nil, false
	}
	getString := func(field string) string {
		if v, ok := getRaw(field); ok && v != nil {
			if s, ok := v.(string); ok {
				return s
			}
		}
		return ""
	}
	getInt := func(field string) int {
		if v, ok := getRaw(field); ok && v != nil {
			switch t := v.(type) {
			case int:
				return t
			case int32:
				return int(t)
			case int64:
				return int(t)
			case float64:
				return int(t)
			case string:
				if i, err := strconv.Atoi(t); err == nil {
					return i
				}
			}
		}
		return 0
	}
	getTime := func(field string) *time.Time {
		if v, ok := getRaw(field); ok && v != nil {
			switch t := v.(type) {
			case string:
				formats := []string{
					"2006-01-02 15:04:05",
					time.RFC3339,
					"2006-01-02T15:04:05Z",
					"2006-01-02T15:04:05.000Z",
					"2006-01-02 15:04:05 Etc/GMT",
					"2006-01-02 15:04:05 GMT",
				}
				for _, f := range formats {
					if parsed, err := time.Parse(f, t); err == nil {
						return &parsed
					}
				}
			case int64:
				pt := time.Unix(t, 0)
				return &pt
			case float64:
				// 支持秒或毫秒
				if t > 1e10 {
					pt := time.Unix(0, int64(t)*int64(time.Millisecond))
					return &pt
				}
				pt := time.Unix(int64(t), 0)
				return &pt
			}
		}
		return nil
	}

	notification := &models.AppleServerNotification{}

	// 映射主键
	if id := getInt("id"); id > 0 {
		notification.ID = uint(id)
	}

	// 直接字段映射（优先 After，回退 Before）
	notification.NotificationUUID = stringPtr(getString("notification_uuid"))
	notification.NotificationType = stringPtr(getString("notification_type"))
	notification.NotificationTypeV2 = stringPtr(getString("notification_type_v2"))
	notification.Subtype = stringPtr(getString("subtype"))
	notification.Version = stringPtr(getString("version"))
	notification.Environment = stringPtr(getString("environment"))
	notification.Summary = stringPtr(getString("summary"))
	notification.Data = stringPtr(getString("data"))
	notification.RequestContent = stringPtr(getString("request_content"))
	notification.DecodedContent = stringPtr(getString("decoded_content"))
	notification.OriginalTransactionID = stringPtr(getString("original_transaction_id"))
	notification.TransactionID = stringPtr(getString("transaction_id"))
	notification.WebOrderLineItemID = stringPtr(getString("web_order_line_item_id"))
	notification.ProductID = stringPtr(getString("product_id"))
	notification.SubscriptionGroupID = stringPtr(getString("subscription_group_id"))

	// Bundle 信息：优先表列 bundle_id / bundle_version；回退旧表命名 app_bundle_id / bvrs
	if b := getString("bundle_id"); b != "" {
		notification.BundleID = &b
	} else if ab := getString("app_bundle_id"); ab != "" {
		notification.BundleID = &ab
	}
	if bv := getString("bundle_version"); bv != "" {
		notification.BundleVersion = &bv
	} else if bvrs := getString("bvrs"); bvrs != "" {
		notification.BundleVersion = &bvrs
	}

	// AppAppleID（如果表里是 app_apple_id）
	if appAppleID := getInt("app_apple_id"); appAppleID > 0 {
		u := uint(appAppleID)
		notification.AppAppleID = &u
	}

	// 时间字段
	notification.ReceiveDate = getTime("receive_date")
	notification.SignedDate = getTime("signed_date")
	notification.PurchaseDate = getTime("purchase_date")
	notification.OriginalPurchaseDate = getTime("original_purchase_date")
	notification.ExpiresDate = getTime("expires_date")
	if notification.ExpiresDate == nil {
		// 兼容 1.0 的 expires_date_formatted 文本
		if s := getString("expires_date_formatted"); s != "" {
			if t := getTime("expires_date_formatted"); t != nil {
				notification.ExpiresDate = t
			}
		}
	}

	// 其他可能存在的整型字段
	if q := getInt("quantity"); q != 0 {
		notification.Quantity = intPtr(q)
	}
	if st := getInt("status"); st != 0 {
		notification.Status = intPtr(st)
	}

	// 其他可选文本字段
	notification.Type = stringPtr(getString("type"))
	notification.InAppOwnershipType = stringPtr(getString("in_app_ownership_type"))
	notification.SignedRenewalInfo = stringPtr(getString("signed_renewal_info"))
	notification.SignedTransactionInfo = stringPtr(getString("signed_transaction_info"))
	notification.ConsumptionRequestReason = stringPtr(getString("consumption_request_reason"))

	// 记录创建/更新时间（如存在）
	if t := getTime("created_at"); t != nil {
		notification.CreatedAt = *t
	}
	if t := getTime("updated_at"); t != nil {
		notification.UpdatedAt = *t
	}

	return notification
}

// extractTransactionIDFromNotification 从通知中提取transaction_id
func (h *SyncHandler) extractTransactionIDFromNotification(notification *models.AppleServerNotification) string {
	// 2.0版本：从decoded_content提取
	if notification.Version != nil && *notification.Version == "2.0" && notification.DecodedContent != nil {
		var decoded map[string]interface{}
		if err := json.Unmarshal([]byte(*notification.DecodedContent), &decoded); err == nil {
			// 尝试多个路径
			if data, ok := decoded["data"].(map[string]interface{}); ok {
				if transaction, ok := data["transaction"].(map[string]interface{}); ok {
					if txID, ok := transaction["transactionId"].(string); ok {
						return txID
					}
				}
			}
			if transaction, ok := decoded["transaction"].(map[string]interface{}); ok {
				if txID, ok := transaction["transactionId"].(string); ok {
					return txID
				}
			}
		}
	}

	// 1.0版本：从request_content提取
	if notification.RequestContent != nil {
		var requestData map[string]interface{}
		if err := json.Unmarshal([]byte(*notification.RequestContent), &requestData); err == nil {
			if unifiedReceipt, ok := requestData["unified_receipt"].(map[string]interface{}); ok {
				if latestReceiptInfo, ok := unifiedReceipt["latest_receipt_info"].([]interface{}); ok && len(latestReceiptInfo) > 0 {
					if latestInfo, ok := latestReceiptInfo[0].(map[string]interface{}); ok {
						if txID, ok := latestInfo["transaction_id"].(string); ok {
							return txID
						}
						if origTxID, ok := latestInfo["original_transaction_id"].(string); ok {
							return origTxID
						}
					}
				}
			}
			if origTxID, ok := requestData["original_transaction_id"].(string); ok {
				return origTxID
			}
		}
	}

	// 兜底：使用表字段
	if notification.OriginalTransactionID != nil {
		return *notification.OriginalTransactionID
	}

	return ""
}

// findAppConfigByDatabase 根据数据库名查找App配置
func (h *SyncHandler) findAppConfigByDatabase(ctx context.Context, database string) (*models.AppConfig, error) {
	var appConfig models.AppConfig
	err := h.db.WithContext(ctx).
		Where("db_name = ?", database).
		First(&appConfig).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &appConfig, nil
}

// isTerminalState 判断是否为终态
func (h *SyncHandler) isTerminalState(refundRequest *models.RefundRequest) bool {
	if refundRequest.RefundResult == nil {
		return false
	}

	result := *refundRequest.RefundResult
	return result == "已退款(订单轮询)" || result == "已退款(收到退款通知)"
}

// processNotificationForRequest 为特定退款请求处理通知
func (h *SyncHandler) processNotificationForRequest(ctx context.Context, notification *models.AppleServerNotification, refundRequest *models.RefundRequest, appConfig *models.AppConfig) error {
	if notification.NotificationType == nil {
		return nil
	}

	switch *notification.NotificationType {
	case "REFUND":
		return h.handleRefundNotification(ctx, notification, refundRequest)
	case "REFUND_DECLINED":
		return h.handleRefundDeclinedNotification(ctx, notification, refundRequest)
	}

	return nil
}

// handleRefundNotification 处理REFUND通知
func (h *SyncHandler) handleRefundNotification(ctx context.Context, notification *models.AppleServerNotification, refundRequest *models.RefundRequest) error {
	refundTime := h.extractRefundTimeFromNotification(notification)

	updates := map[string]interface{}{
		"refund_result":       "已退款(收到退款通知)",
		"refund_success_time": refundTime,
	}

	// 计算退款成功小时数
	if refundTime != nil && refundRequest.FirstPushTime != nil {
		hours := refundTime.Sub(*refundRequest.FirstPushTime).Hours()
		// 保留1位小数
		cleanHours := float64(int(hours*10+0.5)) / 10
		updates["refund_success_hours"] = &cleanHours
	}

	return h.db.WithContext(ctx).Model(refundRequest).Updates(updates).Error
}

// handleRefundDeclinedNotification 处理REFUND_DECLINED通知
func (h *SyncHandler) handleRefundDeclinedNotification(ctx context.Context, notification *models.AppleServerNotification, refundRequest *models.RefundRequest) error {
	declineTime := notification.ExtractDeclineTime()

	updates := map[string]interface{}{
		"last_refund_decline_time": declineTime,
	}

	if refundRequest.FirstRefundDeclineTime == nil {
		updates["first_refund_decline_time"] = declineTime
	}

	return h.db.WithContext(ctx).Model(refundRequest).Updates(updates).Error
}

// extractRefundTimeFromNotification 从REFUND通知中提取退款时间
func (h *SyncHandler) extractRefundTimeFromNotification(notification *models.AppleServerNotification) *time.Time {
	// 2.0版本：从decoded_content的revocationDate提取
	if notification.Version != nil && *notification.Version == "2.0" && notification.DecodedContent != nil {
		var decoded map[string]interface{}
		if err := json.Unmarshal([]byte(*notification.DecodedContent), &decoded); err == nil {
			// 尝试多个路径
			paths := [][]string{
				{"data", "transaction", "revocationDate"},
				{"transaction", "revocationDate"},
			}

			for _, path := range paths {
				if timestamp := getNestedValue(decoded, path); timestamp != nil {
					if ts, ok := timestamp.(float64); ok {
						t := time.Unix(0, int64(ts)*int64(time.Millisecond))
						return &t
					}
				}
			}
		}
	}

	// 1.0版本：从request_content的cancellation_date提取
	if notification.RequestContent != nil {
		var requestData map[string]interface{}
		if err := json.Unmarshal([]byte(*notification.RequestContent), &requestData); err == nil {
			if unifiedReceipt, ok := requestData["unified_receipt"].(map[string]interface{}); ok {
				if latestReceiptInfo, ok := unifiedReceipt["latest_receipt_info"].([]interface{}); ok && len(latestReceiptInfo) > 0 {
					if latestInfo, ok := latestReceiptInfo[0].(map[string]interface{}); ok {
						if cancellationDate, ok := latestInfo["cancellation_date"].(string); ok {
							if t, err := time.Parse("2006-01-02 15:04:05 Etc/GMT", cancellationDate); err == nil {
								return &t
							}
						}
					}
				}
			}
		}
	}

	// 兜底：使用通知接收时间
	return notification.ExtractRefundTime()
}

// 辅助函数
func stringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

func safeStr(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func getNestedValue(data map[string]interface{}, path []string) interface{} {
	current := data
	for i, key := range path {
		if i == len(path)-1 {
			return current[key]
		}
		if next, ok := current[key].(map[string]interface{}); ok {
			current = next
		} else {
			return nil
		}
	}
	return nil
}

// getRandomScheduleDelayHours 获取随机调度延迟小时数
func (h *SyncHandler) getRandomScheduleDelayHours() int {
	cfg := config.Get()
	if cfg != nil && len(cfg.Refund.SchedProcessDelayHours) > 0 {
		options := cfg.Refund.SchedProcessDelayHours
		return options[rand.Intn(len(options))]
	}

	// 默认延迟选项：0, 6, 12, 24, 48小时
	options := []int{0, 6, 12, 24, 48}
	return options[rand.Intn(len(options))]
}

// getRefundPolicyID 获取退款策略ID
func (h *SyncHandler) getRefundPolicyID(appConfig *models.AppConfig) int {
	// 优先使用App配置的退款策略ID
	if appConfig.RefundPolicyID != nil && *appConfig.RefundPolicyID != "" {
		policyIDs := strings.Split(*appConfig.RefundPolicyID, ",")
		if len(policyIDs) > 0 {
			if id, err := strconv.Atoi(strings.TrimSpace(policyIDs[rand.Intn(len(policyIDs))])); err == nil && id > 0 {
				return id
			}
		}
	}

	// // 使用配置中的默认策略ID
	// cfg := config.Get()
	// if cfg != nil && cfg.Refund.DefaultPolicyID > 0 {
	// 	return cfg.Refund.DefaultPolicyID
	// }

	// todo: 数据库配置, 是否还需要读配置 支持
	// 最后的默认策略ID
	return 9
}

// associateOrderInfo 关联订单信息
func (h *SyncHandler) associateOrderInfo(ctx context.Context, refundRequest *models.RefundRequest, appConfig *models.AppConfig) {
	if refundRequest.TransactionID == nil || appConfig.AdamID == nil {
		return
	}

	// 查找订单信息
	order, err := models.GetOrderByTransactionID(h.db.WithContext(ctx), *appConfig.AdamID, *refundRequest.TransactionID)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"adam_id":        *appConfig.AdamID,
			"transaction_id": *refundRequest.TransactionID,
		}).Debug("failed to find order for refund request")
		return
	}

	// 更新退款请求的订单相关信息
	updates := map[string]interface{}{}

	if order.FirstOriginalTransactionID != nil {
		updates["first_original_transaction_id"] = *order.FirstOriginalTransactionID
	}

	regionID, _ := order.GetRegionInfo()
	if regionID != "" {
		updates["region_id"] = regionID
	}

	if order.SKUDisplayName != nil {
		updates["sku_display_name"] = *order.SKUDisplayName
	}

	if order.Price != nil {
		updates["sku_price"] = *order.Price
	}

	if order.OrderTime != nil {
		updates["order_time"] = *order.OrderTime

		// 计算订单到退款请求的小时数
		if refundRequest.FirstPushTime != nil {
			hours := order.CalculateOrderToRefundRequestHours(*refundRequest.FirstPushTime)
			updates["order_to_refund_request_hours"] = hours
		}
	}

	if len(updates) > 0 {
		_ = h.db.WithContext(ctx).Model(refundRequest).Updates(updates).Error
	}
}

// 辅助函数
func intPtr(i int) *int {
	if i == 0 {
		return nil
	}
	return &i
}

func uintPtr(i int) *uint {
	if i == 0 {
		return nil
	}
	u := uint(i)
	return &u
}

// handleConsumptionRequestNotification 处理CONSUMPTION_REQUEST通知，创建新的退款请求记录
// 相当于PHP中CohortSyncRefundRequests的逻辑
func (h *SyncHandler) handleConsumptionRequestNotification(ctx context.Context, notification *models.AppleServerNotification, appConfig *models.AppConfig) error {
	transactionID := h.extractTransactionIDFromNotification(notification)
	if transactionID == "" {
		h.logger.Warn("failed to extract transaction_id from CONSUMPTION_REQUEST notification")
		return nil
	}

	h.logger.WithFields(map[string]interface{}{
		"notification_id": notification.ID,
		"transaction_id":  transactionID,
		"adam_id":         safeInt64(appConfig.AdamID),
	}).Info("creating refund request from CONSUMPTION_REQUEST notification")

	// 检查是否已存在退款请求
	var existing models.RefundRequest
	err := h.db.WithContext(ctx).
		Where("adam_id = ? AND transaction_id = ?", safeInt64(appConfig.AdamID), transactionID).
		First(&existing).Error

	if err == nil {
		// 已存在，更新推送相关字段（模拟原PHP逻辑中的推送计数更新）
		h.logger.WithFields(map[string]interface{}{
			"refund_request_id": existing.ID,
			"transaction_id":    transactionID,
		}).Info("refund request already exists, updating push info")

		return h.updateExistingRefundRequestFromNotification(ctx, notification, &existing)
	}

	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing refund request: %w", err)
	}

	// 不存在，创建新的退款请求记录
	return h.createRefundRequestFromNotification(ctx, notification, appConfig)
}

// updateExistingRefundRequestFromNotification 从通知更新已存在的退款请求
func (h *SyncHandler) updateExistingRefundRequestFromNotification(ctx context.Context, notification *models.AppleServerNotification, existing *models.RefundRequest) error {
	updates := map[string]interface{}{}

	// 更新最后推送时间
	if notification.ReceiveDate != nil {
		updates["last_push_time"] = notification.ReceiveDate
	}

	// 增加推送计数
	currentCnt := 0
	if existing.PushCnt != nil {
		currentCnt = *existing.PushCnt
	}
	pushCnt := currentCnt + 1 // 总是递增
	updates["push_cnt"] = pushCnt

	if len(updates) == 0 {
		return nil
	}

	return h.db.WithContext(ctx).Model(existing).Updates(updates).Error
}

// createRefundRequestFromNotification 从CONSUMPTION_REQUEST通知创建新的退款请求记录
func (h *SyncHandler) createRefundRequestFromNotification(ctx context.Context, notification *models.AppleServerNotification, appConfig *models.AppConfig) error {
	// todo: 数据库配置, 是否还需要读配置
	schedDelayHours := 0
	// // 获取调度处理延迟配置
	// schedDelayHours := h.getRandomScheduleDelayHours()

	// 获取退款策略ID
	refundPolicyID := h.getRefundPolicyID(appConfig)

	// 构建退款请求记录
	firstPushTime := notification.ReceiveDate
	if firstPushTime == nil {
		now := time.Now()
		firstPushTime = &now
	}

	schedProcessTime := firstPushTime.Add(time.Duration(schedDelayHours) * time.Hour)
	now := time.Now()

	// todo: apple_notifications_last_sync_id 不需要记录了, 如果要记录, 需要记录大于当前的 id
	refundRequest := &models.RefundRequest{
		AppID:                  uintToIntPtr(appConfig.ID),
		DeveloperID:            appConfig.DeveloperID,
		AdamID:                 appConfig.AdamID,
		AppName:                appConfig.AppName,
		OriginalRecordID:       intPtr64(int64(notification.ID)), // todo: 这个原始记录ID 意义变了, 需要评估新建字段
		OaSyncTime:             &now,
		SchedProcessDelayHours: &schedDelayHours,
		SchedProcessTime:       &schedProcessTime,
		RefundPolicyID:         &refundPolicyID,

		// 原先代码计算包含
		NotificationID: intPtr64(int64(notification.ID)),
		TheDay:         firstPushTime.Format("2006-01-02"),
		Environment:    notification.Environment,
		AppBundleID:    notification.BundleID,
		ProductID:      notification.ProductID,
		// FirstOriginalTransactionID: notification.OriginalTransactionID, // todo: 可以从 order 里拿
		OriginalTransactionID: notification.OriginalTransactionID,
		TransactionID:         stringPtr(h.extractTransactionIDFromNotification(notification)),
		FirstPushTime:         firstPushTime,
		LastPushTime:          firstPushTime,
		PushCnt:               intPtr(1),
	}

	// 从通知中提取更多字段
	appAccountToken := h.extractAppAccountTokenFromNotification(notification)
	if appAccountToken != "" {
		refundRequest.AppAccountToken = &appAccountToken
	}

	// 获取退款原因（从decoded_content中提取）
	refundReason := h.extractRefundReasonFromNotification(notification)
	if refundReason != "" {
		refundRequest.RefundReason = &refundReason
	}

	// 创建记录
	if err := h.db.WithContext(ctx).Create(refundRequest).Error; err != nil {
		return fmt.Errorf("failed to create refund request: %w", err)
	}

	// 关联订单信息
	h.associateOrderInfo(ctx, refundRequest, appConfig)

	return nil
}

// extractRefundReasonFromNotification 从通知的decoded_content中提取退款原因
func (h *SyncHandler) extractRefundReasonFromNotification(notification *models.AppleServerNotification) string {
	// todo: 这里需要处理错误, 区分记录不存在和不符合预期
	if notification.DecodedContent == nil {
		return ""
	}

	var decoded map[string]interface{}
	if err := json.Unmarshal([]byte(*notification.DecodedContent), &decoded); err != nil {
		return ""
	}

	if data, ok := decoded["data"].(map[string]interface{}); ok {
		if reason, ok := data["consumptionRequestReason"].(string); ok {
			return reason
		}
	}

	return ""
}

// extractAppAccountTokenFromNotification 从通知的decoded_content中提取app_account_token
func (h *SyncHandler) extractAppAccountTokenFromNotification(notification *models.AppleServerNotification) string {
	if notification.DecodedContent == nil {
		return ""
	}

	var decoded map[string]interface{}
	if err := json.Unmarshal([]byte(*notification.DecodedContent), &decoded); err != nil {
		return ""
	}

	// 尝试多个路径提取 appAccountToken
	paths := [][]string{
		{"data", "appAccountToken"},
		{"appAccountToken"},
		{"data", "transaction", "appAccountToken"},
	}

	for _, path := range paths {
		if token := getNestedValue(decoded, path); token != nil {
			if tokenStr, ok := token.(string); ok {
				return tokenStr
			}
		}
	}

	return ""
}

// 辅助函数
func safeUint(u *uint) uint {
	if u == nil {
		return 0
	}
	return *u
}

func safeInt64(i *int64) int64 {
	if i == nil {
		return 0
	}
	return *i
}

func intPtr64(i int64) *int64 {
	return &i
}

func uintToIntPtr(u uint) *int {
	i := int(u)
	return &i
}
