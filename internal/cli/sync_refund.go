package cli

import (
	"context"
	"fmt"
	"midas/internal/client"
	"midas/internal/config"
	"midas/internal/pkg/binlog"
	"midas/internal/pkg/ctxx"
	"midas/internal/pkg/logx"
	"midas/internal/pkg/refund"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/cobra"
)

var syncRefundCmd = &cobra.Command{
	Use:   "sync-refund",
	Short: "基于binlog订阅同步退款相关数据",
	Long: `基于Kafka binlog订阅同步Apple服务器通知数据，统一处理退款相关业务。

功能说明：
1. 订阅apple_server_notifications表的binlog变更，实时处理所有退款相关通知
2. 支持三种通知类型：
   - CONSUMPTION_REQUEST（消费请求）：创建新的退款请求记录
   - REFUND（退款成功）：更新退款请求为已退款状态
   - REFUND_DECLINED（退款拒绝）：记录退款拒绝信息
3. 整合两个PHP命令的逻辑，实现统一的退款数据同步：
   - 相当于CohortSyncRefundRequests：从CONSUMPTION_REQUEST通知创建退款请求
   - 相当于CohortSyncAppleNotifications：从REFUND/REFUND_DECLINED通知更新状态
4. 支持多数据库、多App的并发处理
5. 提供完整的错误处理和日志记录

使用示例：
  midas sync-refund

注意事项：
- 确保Kafka和Debezium连接器正常运行
- 确保配置文件中的apple_notifications_cdc订阅配置正确
- 建议在生产环境中配置适当的消费组ID以支持水平扩展`,
	RunE: func(cmd *cobra.Command, args []string) error {
		base := ctxx.WithBothIDs(cmd.Context())

		// 应用配置的超时时间
		cfg := config.Get()
		if cfg == nil {
			return fmt.Errorf("config not loaded")
		}

		var ctx context.Context
		var cancel context.CancelFunc

		// 检查是否配置了 debug_timeout
		if cfg.Refund.DebugTimeout != "" {
			if timeout, err := time.ParseDuration(cfg.Refund.DebugTimeout); err != nil {
				return fmt.Errorf("invalid debug_timeout format: %w", err)
			} else if timeout > 0 {
				ctx, cancel = context.WithTimeout(base, timeout)
			} else {
				// <= 0 表示不过期
				ctx, cancel = signal.NotifyContext(base, os.Interrupt, syscall.SIGTERM)
			}
		} else {
			// 未配置则不过期
			ctx, cancel = signal.NotifyContext(base, os.Interrupt, syscall.SIGTERM)
		}
		defer cancel()

		return syncRefundHandler(ctx)
	},
}

func syncRefundHandler(ctx context.Context) error {
	logger := logx.WithContext(ctx)
	logger.Info("starting refund sync service")

	cfg := config.Get()
	if cfg == nil {
		return fmt.Errorf("config not loaded")
	}

	// 初始化数据库连接
	db := client.GetDB()
	if db == nil {
		return fmt.Errorf("database client not initialized")
	}

	// 初始化Kafka客户端
	kafka := client.GetKafka()
	if kafka == nil {
		return fmt.Errorf("kafka client not initialized")
	}

	// 创建退款同步处理器
	syncHandler := refund.NewSyncHandler(db, logger)

	// 过滤出退款相关的订阅
	refundSubscriptions := filterRefundSubscriptions(cfg.Kafka.Subscriptions)
	if len(refundSubscriptions) == 0 {
		return fmt.Errorf("no refund-related subscriptions found or enabled")
	}

	logger.WithField("subscriptions", len(refundSubscriptions)).Info("starting refund subscriptions")

	// 启动多订阅消费
	return kafka.ConsumeSubscriptions(ctx, refundSubscriptions, func(ctx context.Context, subscription config.KafkaSubscription, message client.Message) error {
		return handleRefundMessage(ctx, syncHandler, subscription, message, logger)
	})
}

// filterRefundSubscriptions 过滤出退款相关的订阅
func filterRefundSubscriptions(subscriptions []config.KafkaSubscription) []config.KafkaSubscription {
	var refundSubs []config.KafkaSubscription

	for _, sub := range subscriptions {
		if !sub.Enabled {
			continue
		}

		// 检查是否为退款相关订阅
		// apple_notifications_cdc: 处理apple_server_notifications表变更
		//   - CONSUMPTION_REQUEST通知：创建新退款请求记录
		//   - REFUND/REFUND_DECLINED通知：更新退款请求状态
		if sub.Name == "apple_notifications_cdc" {
			refundSubs = append(refundSubs, sub)
		}
	}

	return refundSubs
}

// handleRefundMessage 处理退款相关消息
func handleRefundMessage(ctx context.Context, syncHandler *refund.SyncHandler, subscription config.KafkaSubscription, message client.Message, logger logx.Logger) error {
	msgLogger := logger.WithFields(map[string]interface{}{
		"subscription": subscription.Name,
		"topic":        message.Topic,
		"partition":    message.Partition,
		"offset":       message.Offset,
	})

	// 解析Debezium消息，包含headers信息
	event, err := binlog.ParseDebeziumMessageWithHeaders(message.Value, message.Headers)
	if err != nil {
		msgLogger.WithError(err).Error("failed to parse debezium message")
		return err
	}

	// 记录事件信息
	msgLogger = msgLogger.WithFields(map[string]interface{}{
		"operation": event.Operation,
		"database":  event.Database,
		"table":     event.Table,
	})

	msgLogger.Debug("received binlog event")

	// 根据表类型分发处理
	switch {
	case event.IsAppleServerNotification():
		msgLogger.Info("processing apple server notification")
		return syncHandler.HandleAppleServerNotification(ctx, event)

	default:
		msgLogger.Debug("skipping non-refund related table")
		return nil
	}
}

func init() {
	rootCmd.AddCommand(syncRefundCmd)
}
