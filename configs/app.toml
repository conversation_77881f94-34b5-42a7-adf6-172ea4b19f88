# Midas 支付离线功能配置文件

[app]
name = "midas"
env = "development"  # development, staging, production
log_dir = "./logs"

[http]
timeout = "10s"
retry_count = 3
retry_wait = "1s"

[mysql]
# MySQL 连接配置 - 业务代码使用 root/root@123 账号
dsn = "root:root@123@tcp(127.0.0.1:3306)/cohort?charset=utf8mb4&parseTime=True&loc=Local"
max_idle = 10
max_open = 100
conn_max_lifetime = "1h"

[redis]
# 预留给用户配置  
# addr = "localhost:6379"
# password = ""
db = 0
username = ""
pool_size = 10

[refund]
# 退款处理配置
default_policy_id = 1
sched_process_delay_hours = [0, 6, 12, 24, 48]
max_processing_days = 3
# sync-refund 命令超时时间，空字符串或 <=0 表示不过期
debug_timeout = "3000s"

[apple]
# Apple Store Connect API 配置
# 预留给用户配置具体的 issuer_id, key_id, private_key 等
timeout = "30s"

[kafka]
brokers = ["localhost:19092"]
## 多订阅配置 - 每个订阅是一个独立的消费链路
#[[kafka.subscriptions]]
#name = "accounts_cdc"           # 订阅名称
#group_id = "midas-accounts-cdc-v1"  # 消费组ID（支持版本号）
#topics = ["midas.test_uzen.accounts", "midas.prod_uzen.accounts"]
#enabled = true
#description = "账户CDC数据同步"

# Apple Server Notifications 订阅配置
# 处理退款状态更新通知：REFUND, REFUND_DECLINED
[[kafka.subscriptions]]
name = "apple_notifications_cdc"
group_id = "midas-apple-notifications-cdc-v1"
topics = ["apps.*.apple_server_notifications"]
enabled = true
description = "Apple服务器通知CDC数据同步（处理REFUND/REFUND_DECLINED通知）"
#
#[[kafka.subscriptions]]
#name = "transactions_cdc"       # 订阅名称
#group_id = "midas-transactions-cdc-v1"  # 消费组ID（支持版本号）
#topics = ["midas.test_uzen.transactions", "midas.prod_uzen.transactions"]
#enabled = true
#description = "交易CDC数据同步"
#
#[[kafka.subscriptions]]
#name = "events_realtime"        # 订阅名称
#group_id = "midas-events-realtime-v1"   # 消费组ID（支持版本号）
#topics = ["midas.events.user_actions", "midas.events.system_alerts"]
#enabled = false  # 可以通过配置启用/禁用
#description = "实时事件处理"
#
## 灰度发布示例配置
#[[kafka.subscriptions]]
#name = "accounts_cdc_canary"    # 金丝雀版本
#group_id = "midas-accounts-cdc-v2"  # 新版本
#topics = ["midas.test_uzen.accounts"]
#enabled = false  # 灰度时启用
#description = "账户CDC数据同步 - 金丝雀版本"
